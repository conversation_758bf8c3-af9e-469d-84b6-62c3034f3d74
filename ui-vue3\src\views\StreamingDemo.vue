<template>
  <div class="streaming-demo-container">
    <div class="max-w-6xl mx-auto p-6">
      <h1 class="text-3xl font-bold mb-6 text-center">🎯 流式代码生成演示</h1>
      
      <!-- 控制面板 -->
      <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">测试控制</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label class="block text-sm font-medium mb-2">文件路径</label>
            <input 
              v-model="filePath" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
              placeholder="workspace/demo/Example.java"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-2">文件类型</label>
            <select v-model="fileType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
              <option value="java">Java</option>
              <option value="javascript">JavaScript</option>
              <option value="typescript">TypeScript</option>
              <option value="python">Python</option>
              <option value="html">HTML</option>
              <option value="css">CSS</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-2">生成速度</label>
            <select v-model="speed" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
              <option value="slow">慢速 (100ms)</option>
              <option value="normal">正常 (50ms)</option>
              <option value="fast">快速 (20ms)</option>
            </select>
          </div>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-2">代码生成提示</label>
          <textarea 
            v-model="prompt" 
            rows="3" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
            placeholder="描述你想要生成的代码..."
          ></textarea>
        </div>
        
        <div class="flex space-x-4">
          <button 
            @click="startDemo" 
            :disabled="isRunning"
            class="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ isRunning ? '生成中...' : '🚀 开始演示' }}
          </button>
          
          <button 
            @click="stopDemo" 
            :disabled="!isRunning"
            class="px-6 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            🛑 停止
          </button>
          
          <button 
            @click="clearDemo" 
            class="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
          >
            🗑️ 清除
          </button>
        </div>
      </div>
      
      <!-- 状态显示 -->
      <div v-if="status" class="mb-6">
        <div :class="statusClass" class="px-4 py-3 rounded-md">
          <div class="flex items-center">
            <span class="mr-2">{{ statusIcon }}</span>
            <span>{{ status }}</span>
          </div>
        </div>
      </div>
      
      <!-- 主要演示区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 左侧：文件浏览器模拟 -->
        <div class="bg-gray-900 rounded-lg p-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-white font-semibold">📁 文件浏览器</h3>
            <div class="text-gray-400 text-sm">{{ files.length }} 个文件</div>
          </div>
          
          <div class="space-y-2">
            <div 
              v-for="file in files" 
              :key="file.path"
              :class="[
                'flex items-center p-2 rounded cursor-pointer transition-colors',
                file.path === selectedFile ? 'bg-blue-600 text-white' : 'text-gray-300 hover:bg-gray-700'
              ]"
              @click="selectFile(file.path)"
            >
              <span class="mr-2">{{ getFileIcon(file.type) }}</span>
              <span class="text-sm">{{ file.name }}</span>
              <span v-if="file.isNew" class="ml-auto text-xs bg-green-500 text-white px-2 py-1 rounded">新建</span>
            </div>
          </div>
          
          <div v-if="files.length === 0" class="text-gray-500 text-center py-8">
            暂无文件
          </div>
        </div>
        
        <!-- 右侧：代码编辑器模拟 -->
        <div class="bg-gray-900 rounded-lg p-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-white font-semibold">💻 代码编辑器</h3>
            <div class="text-gray-400 text-sm">
              {{ selectedFile || '未选择文件' }} 
              <span v-if="generatedCode.length > 0">({{ generatedCode.length }} 字符)</span>
            </div>
          </div>
          
          <div class="bg-black rounded p-4 min-h-96 font-mono text-sm">
            <div v-if="!selectedFile" class="text-gray-500 text-center py-8">
              请选择一个文件查看内容
            </div>
            <div v-else-if="generatedCode.length === 0" class="text-gray-500">
              {{ isRunning ? '正在生成代码...' : '文件为空' }}
              <span v-if="isRunning" class="animate-pulse">|</span>
            </div>
            <pre v-else class="text-green-400 whitespace-pre-wrap">{{ generatedCode }}<span v-if="isRunning" class="animate-pulse text-white">|</span></pre>
          </div>
        </div>
      </div>
      
      <!-- 进度和日志 -->
      <div class="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 进度条 -->
        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="font-semibold mb-3">📊 生成进度</h3>
          <div class="space-y-3">
            <div>
              <div class="flex justify-between text-sm mb-1">
                <span>整体进度</span>
                <span>{{ Math.round(progress) }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div 
                  class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  :style="{ width: progress + '%' }"
                ></div>
              </div>
            </div>
            
            <div class="text-sm text-gray-600">
              <div>已生成: {{ generatedCode.length }} 字符</div>
              <div>预计总长度: {{ estimatedLength }} 字符</div>
              <div>生成速度: {{ currentSpeed }}ms/块</div>
            </div>
          </div>
        </div>
        
        <!-- 事件日志 -->
        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="font-semibold mb-3">📝 事件日志</h3>
          <div class="max-h-48 overflow-y-auto space-y-1">
            <div 
              v-for="(log, index) in logs" 
              :key="index"
              class="text-xs p-2 rounded"
              :class="getLogClass(log.type)"
            >
              <span class="text-gray-500">{{ log.time }}</span>
              <span class="ml-2 font-medium">{{ log.type }}</span>
              <span class="ml-2">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 响应式数据
const filePath = ref('workspace/demo/Example.java')
const fileType = ref('java')
const speed = ref('normal')
const prompt = ref('创建一个简单的Hello World Java类，包含main方法和一些基本功能')
const isRunning = ref(false)
const status = ref('')
const statusType = ref<'success' | 'error' | 'info' | 'warning'>('info')
const generatedCode = ref('')
const selectedFile = ref('')
const progress = ref(0)
const estimatedLength = ref(1000)

// 文件列表
const files = ref<Array<{path: string, name: string, type: string, isNew?: boolean}>>([])

// 日志
const logs = ref<Array<{time: string, type: string, message: string}>>([])

// 计算属性
const statusClass = computed(() => ({
  'bg-green-100 text-green-800 border border-green-200': statusType.value === 'success',
  'bg-red-100 text-red-800 border border-red-200': statusType.value === 'error',
  'bg-blue-100 text-blue-800 border border-blue-200': statusType.value === 'info',
  'bg-yellow-100 text-yellow-800 border border-yellow-200': statusType.value === 'warning',
}))

const statusIcon = computed(() => {
  switch (statusType.value) {
    case 'success': return '✅'
    case 'error': return '❌'
    case 'warning': return '⚠️'
    default: return 'ℹ️'
  }
})

const currentSpeed = computed(() => {
  switch (speed.value) {
    case 'slow': return 100
    case 'fast': return 20
    default: return 50
  }
})

// 方法
const addLog = (type: string, message: string) => {
  logs.value.push({
    time: new Date().toLocaleTimeString(),
    type,
    message
  })
  
  // 保持日志数量在合理范围内
  if (logs.value.length > 50) {
    logs.value.shift()
  }
}

const setStatus = (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info') => {
  status.value = message
  statusType.value = type
}

const getFileIcon = (type: string) => {
  const icons = {
    java: '☕',
    javascript: '🟨',
    typescript: '🔷',
    python: '🐍',
    html: '🌐',
    css: '🎨'
  }
  return icons[type] || '📄'
}

const getLogClass = (type: string) => {
  if (type.includes('ERROR')) return 'bg-red-50 text-red-700'
  if (type.includes('SUCCESS')) return 'bg-green-50 text-green-700'
  if (type.includes('START')) return 'bg-blue-50 text-blue-700'
  return 'bg-gray-50 text-gray-700'
}

const selectFile = (path: string) => {
  selectedFile.value = path
  addLog('FILE_SELECTED', `选择文件: ${path}`)
}

const startDemo = async () => {
  if (isRunning.value) return
  
  isRunning.value = true
  generatedCode.value = ''
  progress.value = 0
  
  try {
    // 步骤1: 创建空文件
    addLog('DEMO_START', '开始演示流程')
    setStatus('正在创建空文件...', 'info')
    
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 添加文件到文件列表
    const fileName = filePath.value.split('/').pop() || 'unknown'
    const newFile = {
      path: filePath.value,
      name: fileName,
      type: fileType.value,
      isNew: true
    }
    
    files.value.push(newFile)
    selectedFile.value = filePath.value
    
    addLog('FILE_CREATED', `空文件创建成功: ${fileName}`)
    setStatus('空文件创建成功，开始生成代码...', 'success')
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 步骤2: 模拟流式代码生成
    addLog('CODE_GENERATION_START', '开始流式代码生成')
    setStatus('正在流式生成代码...', 'info')
    
    const sampleCode = getSampleCode(fileType.value)
    estimatedLength.value = sampleCode.length
    
    // 逐字符生成代码
    for (let i = 0; i < sampleCode.length; i++) {
      if (!isRunning.value) break
      
      generatedCode.value += sampleCode[i]
      progress.value = (i / sampleCode.length) * 100
      
      // 每10个字符记录一次进度
      if (i % 10 === 0) {
        addLog('CODE_CHUNK', `生成进度: ${Math.round(progress.value)}%`)
      }
      
      await new Promise(resolve => setTimeout(resolve, currentSpeed.value))
    }
    
    if (isRunning.value) {
      progress.value = 100
      addLog('CODE_GENERATION_COMPLETE', '代码生成完成')
      setStatus('代码生成完成！', 'success')
      
      // 移除新建标记
      const file = files.value.find(f => f.path === filePath.value)
      if (file) file.isNew = false
    }
    
  } catch (error) {
    addLog('DEMO_ERROR', `演示出错: ${error}`)
    setStatus('演示过程中出现错误', 'error')
  } finally {
    isRunning.value = false
  }
}

const stopDemo = () => {
  isRunning.value = false
  addLog('DEMO_STOPPED', '用户停止了演示')
  setStatus('演示已停止', 'warning')
}

const clearDemo = () => {
  isRunning.value = false
  generatedCode.value = ''
  selectedFile.value = ''
  progress.value = 0
  files.value = []
  logs.value = []
  status.value = ''
  addLog('DEMO_CLEARED', '演示数据已清除')
}

const getSampleCode = (type: string) => {
  const samples = {
    java: `public class Example {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
        
        // 创建一个简单的计算器
        Calculator calc = new Calculator();
        int result = calc.add(5, 3);
        System.out.println("5 + 3 = " + result);
    }
}

class Calculator {
    public int add(int a, int b) {
        return a + b;
    }
    
    public int subtract(int a, int b) {
        return a - b;
    }
    
    public int multiply(int a, int b) {
        return a * b;
    }
    
    public double divide(int a, int b) {
        if (b == 0) {
            throw new IllegalArgumentException("除数不能为零");
        }
        return (double) a / b;
    }
}`,
    javascript: `// JavaScript 工具函数库
const Utils = {
    // 字符串处理
    capitalize: (str) => {
        return str.charAt(0).toUpperCase() + str.slice(1);
    },
    
    // 数组去重
    unique: (arr) => {
        return [...new Set(arr)];
    },
    
    // 深拷贝
    deepClone: (obj) => {
        return JSON.parse(JSON.stringify(obj));
    },
    
    // 防抖函数
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// 使用示例
console.log(Utils.capitalize("hello world"));
console.log(Utils.unique([1, 2, 2, 3, 3, 4]));`,
    python: `# Python 计算器类
class Calculator:
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def subtract(self, a, b):
        result = a - b
        self.history.append(f"{a} - {b} = {result}")
        return result
    
    def multiply(self, a, b):
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result
    
    def divide(self, a, b):
        if b == 0:
            raise ValueError("除数不能为零")
        result = a / b
        self.history.append(f"{a} / {b} = {result}")
        return result
    
    def get_history(self):
        return self.history
    
    def clear_history(self):
        self.history = []

# 使用示例
if __name__ == "__main__":
    calc = Calculator()
    print("计算器演示:")
    print(f"5 + 3 = {calc.add(5, 3)}")
    print(f"10 - 4 = {calc.subtract(10, 4)}")
    print(f"6 * 7 = {calc.multiply(6, 7)}")
    print(f"15 / 3 = {calc.divide(15, 3)}")
    
    print("\\n计算历史:")
    for record in calc.get_history():
        print(record)`,
    html: `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>示例页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .content {
            line-height: 1.6;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>欢迎来到示例页面</h1>
            <p>这是一个简单的HTML页面演示</p>
        </div>
        
        <div class="content">
            <h2>功能特点</h2>
            <ul>
                <li>响应式设计</li>
                <li>现代化样式</li>
                <li>清晰的结构</li>
                <li>易于维护</li>
            </ul>
            
            <h2>联系我们</h2>
            <p>如果您有任何问题，请随时联系我们。</p>
        </div>
    </div>
</body>
</html>`,
    css: `/* 现代化CSS样式 */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    text-decoration: none;
    font-size: 16px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
}

.card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .btn {
        width: 100%;
        text-align: center;
    }
}`,
    typescript: `// TypeScript 示例组件
interface User {
    id: number;
    name: string;
    email: string;
    role: 'admin' | 'user' | 'guest';
}

interface ApiResponse<T> {
    success: boolean;
    data: T;
    message?: string;
}

class UserService {
    private baseUrl: string = '/api/users';
    
    async getUsers(): Promise<ApiResponse<User[]>> {
        try {
            const response = await fetch(this.baseUrl);
            const data = await response.json();
            return {
                success: true,
                data: data
            };
        } catch (error) {
            return {
                success: false,
                data: [],
                message: error instanceof Error ? error.message : '未知错误'
            };
        }
    }
    
    async createUser(userData: Omit<User, 'id'>): Promise<ApiResponse<User>> {
        try {
            const response = await fetch(this.baseUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });
            
            const data = await response.json();
            return {
                success: true,
                data: data
            };
        } catch (error) {
            return {
                success: false,
                data: {} as User,
                message: error instanceof Error ? error.message : '创建用户失败'
            };
        }
    }
}

// 使用示例
const userService = new UserService();

async function loadUsers() {
    const result = await userService.getUsers();
    if (result.success) {
        console.log('用户列表:', result.data);
    } else {
        console.error('加载失败:', result.message);
    }
}`
  }
  
  return samples[type] || '// 示例代码\nconsole.log("Hello, World!");'
}
</script>

<style scoped>
.streaming-demo-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 0;
}
</style>
