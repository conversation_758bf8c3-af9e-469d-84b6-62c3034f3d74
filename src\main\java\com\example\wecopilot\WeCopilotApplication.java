package com.example.wecopilot;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * We-Copilot 应用启动类
 */
@SpringBootApplication
public class WeCopilotApplication {

    public static void main(String[] args) {
        SpringApplication.run(WeCopilotApplication.class, args);
        System.out.println("🚀 We-Copilot 启动成功！");
        System.out.println("📁 文件监控服务已就绪");
        System.out.println("🔗 WebSocket端点: ws://localhost:8080/ws/file-watch");
    }
}
