/**
 * 混合模式代码生成API服务
 * 处理MCP工具创建文件 + 独立代码生成的混合模式
 */

export interface HybridGenerationRequest {
  message: string
  filePath: string
  fileType?: string
}

export interface HybridGenerationResponse {
  success: boolean
  message: string
  taskId?: string
  hybridMode?: boolean
  error?: string
}

export interface CodeGenerationRequest {
  taskId: string
  filePath: string
  prompt: string
  fileType?: string
}

export interface CodeGenerationResponse {
  success: boolean
  message: string
  generationId?: string
  error?: string
}

/**
 * 启动混合模式代码生成
 * 这会先通过MCP工具创建空文件，然后启动独立的代码生成
 */
export async function startHybridGeneration(request: HybridGenerationRequest): Promise<HybridGenerationResponse> {
  try {
    console.log('🚀 启动混合模式代码生成:', request)

    const response = await fetch('/api/chat/hybrid-generation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }

    const data = await response.json()
    console.log('✅ 混合模式响应:', data)

    return data
  } catch (error) {
    console.error('❌ 混合模式请求失败:', error)
    throw error
  }
}

/**
 * 启动独立的代码生成任务
 */
export async function startCodeGeneration(request: CodeGenerationRequest): Promise<CodeGenerationResponse> {
  try {
    console.log('🚀 启动代码生成任务:', request)

    const response = await fetch('/api/code-generation/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }

    const data = await response.json()
    console.log('✅ 代码生成响应:', data)

    return data
  } catch (error) {
    console.error('❌ 代码生成请求失败:', error)
    throw error
  }
}

/**
 * 停止代码生成任务
 */
export async function stopCodeGeneration(generationId: string): Promise<CodeGenerationResponse> {
  try {
    console.log('🛑 停止代码生成任务:', generationId)

    const response = await fetch(`/api/code-generation/stop/${generationId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }

    const data = await response.json()
    console.log('✅ 停止代码生成响应:', data)

    return data
  } catch (error) {
    console.error('❌ 停止代码生成请求失败:', error)
    throw error
  }
}

/**
 * 获取代码生成服务状态
 */
export async function getCodeGenerationStatus(): Promise<{activeTaskCount: number, status: string}> {
  try {
    const response = await fetch('/api/code-generation/status', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('❌ 获取代码生成状态失败:', error)
    throw error
  }
}

/**
 * 智能判断是否应该使用混合模式
 * 基于用户消息内容判断是否需要创建文件并生成代码
 */
export function shouldUseHybridMode(message: string): boolean {
  const hybridKeywords = [
    '创建文件', '生成文件', '写一个', '创建一个', '创建',
    'create file', 'generate file', 'write a', 'create a', 'create',
    '新建', '编写', '实现', '开发', '生成', '写',
    'implement', 'develop', 'build', 'make', 'generate', 'write'
  ]

  const codeKeywords = [
    '.java', '.js', '.ts', '.py', '.cpp', '.c', '.go', '.rs', '.html', '.css',
    'class', 'function', 'method', 'component', 'service', 'servlet', 'controller',
    '类', '函数', '方法', '组件', '服务', '文件', 'file',
    'java', 'javascript', 'typescript', 'python', 'html', 'css'
  ]

  const messageLC = message.toLowerCase()

  const hasHybridKeyword = hybridKeywords.some(keyword =>
    messageLC.includes(keyword.toLowerCase())
  )

  const hasCodeKeyword = codeKeywords.some(keyword =>
    messageLC.includes(keyword.toLowerCase())
  )

  // 更宽松的判断：只要有创建意图或者包含文件扩展名就使用混合模式
  const hasFileExtension = /\.\w+/.test(message)

  console.log('🔍 混合模式判断:', {
    message: message.substring(0, 50) + '...',
    hasHybridKeyword,
    hasCodeKeyword,
    hasFileExtension,
    result: hasHybridKeyword || hasCodeKeyword || hasFileExtension
  })

  return hasHybridKeyword || hasCodeKeyword || hasFileExtension
}

/**
 * 从用户消息中提取文件路径
 * 尝试从消息中智能提取目标文件路径
 */
export function extractFilePathFromMessage(message: string): string | null {
  // 匹配常见的文件路径模式
  const pathPatterns = [
    /(?:文件路径|file path|path)[:：]\s*([^\s]+)/i,
    /(?:创建|create|生成|generate)\s+([^\s]+\.[a-zA-Z]+)/i,
    /([a-zA-Z0-9_\-\/\\\.]+\.[a-zA-Z]+)/g
  ]

  for (const pattern of pathPatterns) {
    const matches = message.match(pattern)
    if (matches) {
      for (const match of matches) {
        const path = match.includes(':') ? match.split(/[:：]/)[1].trim() : match
        // 验证是否看起来像文件路径
        if (path.includes('.') && !path.includes(' ') && path.length > 3) {
          // 确保路径以workspace开头
          if (!path.startsWith('workspace/')) {
            return `workspace/${path}`
          }
          return path
        }
      }
    }
  }

  // 如果没有找到明确的路径，尝试生成一个默认路径
  const fileExtensions = ['.java', '.js', '.ts', '.py', '.html', '.css', '.cpp', '.c']
  for (const ext of fileExtensions) {
    if (message.toLowerCase().includes(ext)) {
      // 根据文件类型生成默认文件名
      const defaultNames = {
        '.java': 'HelloWorld.java',
        '.js': 'script.js',
        '.ts': 'component.ts',
        '.py': 'main.py',
        '.html': 'index.html',
        '.css': 'style.css',
        '.cpp': 'main.cpp',
        '.c': 'main.c'
      }
      return `workspace/test/${defaultNames[ext] || 'file' + ext}`
    }
  }

  return null
}

/**
 * 从文件路径推断文件类型
 */
export function inferFileTypeFromPath(filePath: string): string | undefined {
  const extension = filePath.split('.').pop()?.toLowerCase()
  
  const typeMap: Record<string, string> = {
    'java': 'java',
    'js': 'javascript',
    'ts': 'typescript',
    'py': 'python',
    'cpp': 'cpp',
    'c': 'c',
    'go': 'go',
    'rs': 'rust',
    'html': 'html',
    'css': 'css',
    'json': 'json',
    'xml': 'xml',
    'yml': 'yaml',
    'yaml': 'yaml'
  }

  return extension ? typeMap[extension] : undefined
}
