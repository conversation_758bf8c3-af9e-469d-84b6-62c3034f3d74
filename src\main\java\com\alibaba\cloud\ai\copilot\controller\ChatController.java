package com.alibaba.cloud.ai.copilot.controller;

import com.alibaba.cloud.ai.copilot.dto.ChatRequestDto;
import com.alibaba.cloud.ai.copilot.service.ContinuousConversationService;
import com.alibaba.cloud.ai.copilot.service.ToolExecutionLogger;
import com.alibaba.cloud.ai.copilot.service.StreamingCodeGenerationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 聊天控制器
 * 处理与AI的对话和工具调用
 */
@RestController
@RequestMapping("/api/chat")
public class ChatController {

    private static final Logger logger = LoggerFactory.getLogger(ChatController.class);

    private final ChatClient chatClient;
    private final ContinuousConversationService continuousConversationService;
    private final ToolExecutionLogger executionLogger;
    private final StreamingCodeGenerationService codeGenerationService;

    // 简单的会话存储（生产环境应该使用数据库或Redis）
    private final List<Message> conversationHistory = new ArrayList<>();

    public ChatController(ChatClient chatClient, ContinuousConversationService continuousConversationService,
                         ToolExecutionLogger executionLogger, StreamingCodeGenerationService codeGenerationService) {
        this.chatClient = chatClient;
        this.continuousConversationService = continuousConversationService;
        this.executionLogger = executionLogger;
        this.codeGenerationService = codeGenerationService;
    }

    /**
     * 发送消息给AI - 支持连续工具调用
     */
    @PostMapping("/message")
    public Mono<ChatResponseDto> sendMessage(@RequestBody ChatRequestDto request) {
        return Mono.fromCallable(() -> {
            try {
                logger.info("💬 ========== 新的聊天请求 ==========");
                logger.info("📝 用户消息: {}", request.getMessage());
                logger.info("🕐 请求时间: {}", java.time.LocalDateTime.now());

                // 智能判断是否需要工具调用
                boolean needsToolExecution = continuousConversationService.isLikelyToNeedTools(request.getMessage());
                logger.info("🔍 工具需求分析: {}", needsToolExecution ? "可能需要工具" : "简单对话");

                if (needsToolExecution) {
                    // 需要工具调用的复杂任务 - 使用异步模式
                    String taskId = continuousConversationService.startTask(request.getMessage());
                    logger.info("🆔 任务ID: {}", taskId);

                    // 记录任务开始
                    executionLogger.logToolStatistics(); // 显示当前统计

                    // 异步执行连续对话
                    CompletableFuture.runAsync(() -> {
                        try {
                            logger.info("🚀 开始异步执行连续对话任务: {}", taskId);
                            continuousConversationService.executeContinuousConversation(
                                taskId, request.getMessage(), conversationHistory
                            );
                            logger.info("✅ 连续对话任务完成: {}", taskId);
                        } catch (Exception e) {
                            logger.error("❌ 异步对话执行错误: {}", e.getMessage(), e);
                        }
                    });

                    // 返回异步任务响应
                    ChatResponseDto responseDto = new ChatResponseDto();
                    responseDto.setTaskId(taskId);
                    responseDto.setMessage("任务已启动，正在处理中...");
                    responseDto.setSuccess(true);
                    responseDto.setAsyncTask(true);

                    logger.info("📤 返回响应: taskId={}, 异步任务已启动", taskId);
                    return responseDto;
                } else {
                    // 简单对话 - 使用流式模式
                    logger.info("🔄 执行流式对话处理");

                    // 返回流式响应标识，让前端建立流式连接
                    ChatResponseDto responseDto = new ChatResponseDto();
                    responseDto.setMessage("开始流式对话...");
                    responseDto.setSuccess(true);
                    responseDto.setAsyncTask(false); // 关键：设置为false，表示不是工具任务
                    responseDto.setStreamResponse(true); // 新增：标识为流式响应
                    responseDto.setTotalTurns(1);

                    logger.info("📤 返回流式响应标识");
                    return responseDto;
                }

            } catch (Exception e) {
                logger.error("Error processing chat message", e);
                ChatResponseDto errorResponse = new ChatResponseDto();
                errorResponse.setMessage("Error: " + e.getMessage());
                errorResponse.setSuccess(false);
                return errorResponse;
            }
        });
    }



    /**
     * 流式聊天 - 真正的流式实现
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> streamMessage(@RequestBody ChatRequestDto request) {
        logger.info("🌊 开始流式对话: {}", request.getMessage());

        return Flux.create(sink -> {
            try {
                UserMessage userMessage = new UserMessage(request.getMessage());
                conversationHistory.add(userMessage);

                // 使用Spring AI的流式API
                Flux<String> contentStream = chatClient.prompt()
                    .messages(conversationHistory)
                    .stream()
                    .content();

                // 订阅流式内容并转发给前端
                contentStream
                    .doOnNext(content -> {
                        logger.debug("📨 流式内容片段: {}", content);
                        // 发送SSE格式的数据
                        sink.next("data: " + content + "\n\n");
                    })
                    .doOnComplete(() -> {
                        logger.info("✅ 流式对话完成");
                        sink.next("data: [DONE]\n\n");
                        sink.complete();
                    })
                    .doOnError(error -> {
                        logger.error("❌ 流式对话错误: {}", error.getMessage());
                        sink.error(error);
                    })
                    .subscribe();

            } catch (Exception e) {
                logger.error("❌ 流式对话启动失败: {}", e.getMessage());
                sink.error(e);
            }
        });
    }

    /**
     * 清除对话历史
     */
    @PostMapping("/clear")
    public Mono<Map<String, String>> clearHistory() {
        conversationHistory.clear();
        logger.info("Conversation history cleared");
        return Mono.just(Map.of("status", "success", "message", "Conversation history cleared"));
    }

    /**
     * 混合模式：MCP工具创建文件 + 独立代码生成
     * 这个端点专门处理需要打字机效果的代码生成任务
     */
    @PostMapping("/hybrid-generation")
    public Mono<ChatResponseDto> hybridGeneration(@RequestBody HybridGenerationRequest request) {
        return Mono.fromCallable(() -> {
            try {
                logger.info("🔄 ========== 混合模式代码生成 ==========");
                logger.info("📝 用户消息: {}", request.getMessage());
                logger.info("📁 目标文件: {}", request.getFilePath());
                logger.info("🕐 请求时间: {}", java.time.LocalDateTime.now());

                // 1. 启动MCP工具任务（创建空文件）
                String taskId = continuousConversationService.startTask(request.getMessage());
                logger.info("🆔 MCP任务ID: {}", taskId);

                // 2. 异步执行MCP工具调用（只创建空文件）
                CompletableFuture.runAsync(() -> {
                    try {
                        logger.info("🚀 开始异步执行MCP工具任务: {}", taskId);
                        // 这里会调用create_empty_file工具
                        continuousConversationService.executeContinuousConversation(
                            taskId, "Create an empty file at: " + request.getFilePath(), conversationHistory
                        );

                        // 3. MCP工具完成后，启动独立的代码生成
                        logger.info("📝 MCP工具完成，启动代码生成");
                        codeGenerationService.startCodeGeneration(
                            taskId,
                            request.getFilePath(),
                            request.getMessage(),
                            request.getFileType()
                        );

                        logger.info("✅ 混合模式任务完成: {}", taskId);
                    } catch (Exception e) {
                        logger.error("❌ 混合模式执行错误: {}", e.getMessage(), e);
                    }
                });

                // 返回异步任务响应
                ChatResponseDto responseDto = new ChatResponseDto();
                responseDto.setTaskId(taskId);
                responseDto.setMessage("混合模式任务已启动：正在创建文件并准备代码生成...");
                responseDto.setSuccess(true);
                responseDto.setAsyncTask(true);
                responseDto.setHybridMode(true); // 新增：标识为混合模式

                logger.info("📤 返回混合模式响应: taskId={}", taskId);
                return responseDto;

            } catch (Exception e) {
                logger.error("Error processing hybrid generation", e);
                ChatResponseDto errorResponse = new ChatResponseDto();
                errorResponse.setMessage("Error: " + e.getMessage());
                errorResponse.setSuccess(false);
                return errorResponse;
            }
        });
    }

    /**
     * 获取对话历史
     */
    @GetMapping("/history")
    public Mono<List<MessageDto>> getHistory() {
        List<MessageDto> history = conversationHistory.stream()
            .map(message -> {
                MessageDto dto = new MessageDto();
                dto.setContent(message.getText());
                dto.setRole(message instanceof UserMessage ? "user" : "assistant");
                return dto;
            })
            .toList();

        return Mono.just(history);
    }

    // 注意：Spring AI 1.0.0 使用不同的函数调用方式
    // 函数需要在配置中注册，而不是在运行时动态创建

    public static class ChatResponseDto {
        private String taskId;
        private String message;
        private boolean success;
        private boolean asyncTask;
        private boolean streamResponse; // 新增：标识是否为流式响应
        private boolean hybridMode; // 新增：标识是否为混合模式
        private int totalTurns;
        private boolean reachedMaxTurns;
        private String stopReason;
        private long totalDurationMs;

      public String getTaskId() {
        return taskId;
      }

      public void setTaskId(String taskId) {
        this.taskId = taskId;
      }

      public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

      public boolean isAsyncTask() {
        return asyncTask;
      }

      public void setAsyncTask(boolean asyncTask) {
        this.asyncTask = asyncTask;
      }

      public boolean isStreamResponse() {
        return streamResponse;
      }

      public void setStreamResponse(boolean streamResponse) {
        this.streamResponse = streamResponse;
      }

      public boolean isHybridMode() {
        return hybridMode;
      }

      public void setHybridMode(boolean hybridMode) {
        this.hybridMode = hybridMode;
      }

      public int getTotalTurns() { return totalTurns; }
        public void setTotalTurns(int totalTurns) { this.totalTurns = totalTurns; }

        public boolean isReachedMaxTurns() { return reachedMaxTurns; }
        public void setReachedMaxTurns(boolean reachedMaxTurns) { this.reachedMaxTurns = reachedMaxTurns; }

        public String getStopReason() { return stopReason; }
        public void setStopReason(String stopReason) { this.stopReason = stopReason; }

        public long getTotalDurationMs() { return totalDurationMs; }
        public void setTotalDurationMs(long totalDurationMs) { this.totalDurationMs = totalDurationMs; }
    }

    public static class MessageDto {
        private String content;
        private String role;

        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }

        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
    }

    /**
     * 混合模式生成请求DTO
     */
    public static class HybridGenerationRequest {
        private String message;
        private String filePath;
        private String fileType;

        // 构造器
        public HybridGenerationRequest() {}

        public HybridGenerationRequest(String message, String filePath, String fileType) {
            this.message = message;
            this.filePath = filePath;
            this.fileType = fileType;
        }

        // Getters and Setters
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }

        public String getFileType() { return fileType; }
        public void setFileType(String fileType) { this.fileType = fileType; }

        @Override
        public String toString() {
            return String.format("HybridGenerationRequest{filePath='%s', fileType='%s', messageLength=%d}",
                filePath, fileType, message != null ? message.length() : 0);
        }
    }
}
