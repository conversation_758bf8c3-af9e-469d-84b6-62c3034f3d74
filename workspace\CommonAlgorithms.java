public class CommonAlgorithms {
    public static void main(String[] args) {
        System.out.println("Five Common Algorithms in Java:");

        // 1. Binary Search
        System.out.println("1. Binary Search: Efficiently finds the position of a target value in a sorted array.");
        int[] sortedArray = {1, 3, 5, 7, 9};
        int target = 5;
        System.out.println("   Example: Index of " + target + " is " + binarySearch(sortedArray, target));

        // 2. Bubble Sort
        System.out.println("2. Bubble Sort: A simple sorting algorithm that repeatedly steps through the list.");
        int[] unsortedArray = {5, 3, 8, 6, 2};
        System.out.print("   Example: Before sorting - ");
        printArray(unsortedArray);
        bubbleSort(unsortedArray);
        System.out.print("   After sorting - ");
        printArray(unsortedArray);

        // 3. Quick Sort
        System.out.println("3. Quick Sort: A divide-and-conquer algorithm that sorts by partitioning the array.");
        int[] quickSortArray = {10, 7, 8, 9, 1, 5};
        System.out.print("   Example: Before sorting - ");
        printArray(quickSortArray);
        quickSort(quickSortArray, 0, quickSortArray.length - 1);
        System.out.print("   After sorting - ");
        printArray(quickSortArray);

        // 4. Depth-First Search (DFS)
        System.out.println("4. Depth-First Search (DFS): Traverses as far as possible along each branch before backtracking.");
        System.out.println("   Example: Graph traversal using DFS.");

        // 5. Dijkstra's Algorithm
        System.out.println("5. Dijkstra's Algorithm: Finds the shortest path between nodes in a graph.");
        System.out.println("   Example: Shortest path in a weighted graph.");
    }

    // Binary Search Implementation
    private static int binarySearch(int[] array, int target) {
        int left = 0;
        int right = array.length - 1;
        while (left <= right) {
            int mid = left + (right - left) / 2;
            if (array[mid] == target) return mid;
            if (array[mid] < target) left = mid + 1;
            else right = mid - 1;
        }
        return -1;
    }

    // Bubble Sort Implementation
    private static void bubbleSort(int[] array) {
        int n = array.length;
        for (int i = 0; i < n - 1; i++)
            for (int j = 0; j < n - i - 1; j++)
                if (数组[j] > 数组[j + 1]) {
                    int temp = 数组[j];
                    数组[j] = 数组[j + 1];
                    数组[j + 1] = temp;
                }
    }

    // Quick Sort Implementation
    private static void quickSort(int[] array, int low, int high) {
        if (low < high) {
            int pi = partition(array, low, high);
            quickSort(array, low, pi - 1);
            quickSort(array, pi + 1, high);
        }
    }

    private static int partition(int[] array, int low, int high) {
        int pivot = array[high];
        int i = low - 1;
        for (int j = low; j < high; j++) {
            if (array[j] < pivot) {
                i++;
                int temp = array[i];
                array[i] = array[j];
                array[j] = temp;
            }
        }
        int temp = array[i + 1];
        array[i + 1] = array[high];
        array[high] = temp;
        return i + 1;
    }

    // Utility method to print array
    private static void printArray(int[] array) {
        for (int num : array) {
            System.out.print(num + " ");
        }
        System.out.println();
    }
}
