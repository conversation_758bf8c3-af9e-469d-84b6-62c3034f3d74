# 打字机效果快速开始指南

## 🚀 快速测试

### 方法1：使用测试页面
1. 启动项目后，访问主页 `http://localhost:5173`
2. 点击 **"🎯 测试打字机效果"** 按钮
3. 在测试页面中：
   - 选择一个预设测试用例，或
   - 手动输入文件路径和提示
   - 点击 **"开始测试"**
4. 观察实时代码生成过程

### 方法2：在主界面直接测试
1. 访问编辑器页面 `http://localhost:5173/editor`
2. 在AI聊天区域输入以下测试消息：

```
创建一个HelloWorld.java文件
```

```
生成一个简单的JavaScript工具类 utils.js
```

```
写一个Python计算器 calculator.py
```

## 🎯 测试用例

### Java类生成
**消息**: `创建一个HelloWorld.java文件`
**预期**: 
- 系统判断使用混合模式
- 创建空白文件 `workspace/HelloWorld.java`
- 流式生成Java代码内容

### JavaScript函数
**消息**: `生成一个工具函数文件 utils.js`
**预期**:
- 创建 `workspace/utils.js`
- 逐字符显示JavaScript代码

### Python脚本
**消息**: `写一个Python计算器 calculator.py`
**预期**:
- 创建 `workspace/calculator.py`
- 打字机效果显示Python代码

## 🔍 观察要点

### 1. 模式判断
在浏览器控制台中查看：
```
🔍 混合模式判断: {
  message: "创建一个HelloWorld.java文件...",
  hasHybridKeyword: true,
  hasCodeKeyword: true,
  hasFileExtension: true,
  result: true
}
```

### 2. 文件创建
- 文件浏览器应该立即显示新创建的空白文件
- 文件可以被选中和打开

### 3. 代码生成
- 编辑器区域逐字符显示代码
- 状态栏显示生成进度
- 事件日志记录详细过程

### 4. SSE事件流
观察以下事件序列：
1. `HYBRID_MODE_STARTED` - 混合模式启动
2. `SSE_CONNECTED` - SSE连接建立
3. `CODE_GENERATION_START` - 开始代码生成
4. `CODE_CHUNK` - 代码片段（多次）
5. `CODE_GENERATION_COMPLETE` - 代码生成完成

## ⚠️ 常见问题

### Q: 没有触发混合模式
**解决方案**:
- 确保消息包含文件创建关键词（"创建"、"生成"、"写"）
- 包含文件扩展名（.java、.js、.py等）
- 检查浏览器控制台的判断日志

### Q: 文件没有创建
**检查**:
- workspace目录是否存在
- 文件路径是否正确
- 后端日志是否有错误

### Q: 没有打字机效果
**检查**:
- SSE连接是否建立成功
- 是否收到CODE_CHUNK事件
- 编辑器是否正确处理事件

### Q: 代码生成失败
**检查**:
- API密钥是否配置正确
- 网络连接是否正常
- 后端服务是否正常运行

## 🛠️ 调试技巧

### 1. 浏览器控制台
- 查看网络请求状态
- 观察SSE连接状态
- 检查JavaScript错误

### 2. 后端日志
- Spring Boot控制台输出
- 查看工具调用日志
- SSE推送事件日志

### 3. 文件系统
- 检查workspace目录
- 验证文件是否正确创建
- 确认文件内容

## 📈 性能优化

### 调整生成速度
在 `StreamingCodeGenerationService.java` 中：
```java
// 调整延迟时间控制打字机速度
Thread.sleep(50); // 50ms delay between chunks
```

### 调整块大小
```java
int chunkSize = 1024; // 调整块大小
```

## 🎨 自定义配置

### 修改默认文件路径
在 `hybridGenerationApi.ts` 中修改默认路径生成逻辑

### 添加新的文件类型支持
在 `application.yml` 中添加允许的文件扩展名

### 自定义代码生成提示
在 `StreamingCodeGenerationService.java` 中修改 `buildCodeGenerationPrompt` 方法

## 🚀 下一步

1. **多文件支持**: 扩展为支持同时生成多个相关文件
2. **模板系统**: 添加预定义的代码模板
3. **语法高亮**: 实现实时语法高亮
4. **进度控制**: 添加暂停/继续功能
5. **用户偏好**: 保存用户的生成偏好设置

## 📞 获取帮助

如果遇到问题：
1. 检查本指南的常见问题部分
2. 查看详细的实现文档 `TYPEWRITER_EFFECT_IMPLEMENTATION.md`
3. 检查项目的GitHub Issues
4. 联系开发团队

---

**提示**: 第一次使用时，建议先在测试页面熟悉功能，然后再在主界面中使用。
