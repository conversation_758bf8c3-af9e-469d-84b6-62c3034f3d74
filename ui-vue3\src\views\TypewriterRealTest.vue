<template>
  <div class="typewriter-real-test-container p-6">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-3xl font-bold mb-6 text-center">🎯 真实打字机效果测试</h1>
      
      <!-- 控制面板 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">测试控制</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label class="block text-sm font-medium mb-2">测试消息</label>
            <select v-model="selectedMessage" class="w-full px-3 py-2 border border-gray-300 rounded-md">
              <option value="创建一个HelloWorld.java文件">创建一个HelloWorld.java文件</option>
              <option value="生成一个JavaScript工具类 utils.js">生成一个JavaScript工具类 utils.js</option>
              <option value="写一个Python计算器 calculator.py">写一个Python计算器 calculator.py</option>
              <option value="创建一个HTML页面 index.html">创建一个HTML页面 index.html</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-2">或自定义消息</label>
            <input 
              v-model="customMessage" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="输入自定义测试消息..."
            />
          </div>
        </div>
        
        <div class="flex space-x-4">
          <button 
            @click="sendTestMessage" 
            :disabled="isLoading"
            class="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
          >
            {{ isLoading ? '发送中...' : '🚀 发送测试消息' }}
          </button>
          
          <button 
            @click="clearTest" 
            class="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
          >
            🗑️ 清除
          </button>
        </div>
      </div>
      
      <!-- 状态显示 -->
      <div v-if="status" class="mb-6">
        <div :class="statusClass" class="px-4 py-3 rounded-md">
          {{ status }}
        </div>
      </div>
      
      <!-- 主要测试区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 左侧：文件浏览器 -->
        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-lg font-semibold mb-4">📁 文件浏览器</h3>
          <FileExplorer 
            ref="fileExplorerRef"
            @file-selected="handleFileSelected"
            @refresh="handleRefresh"
          />
        </div>
        
        <!-- 右侧：代码编辑器 -->
        <div class="bg-white rounded-lg shadow p-4">
          <h3 class="text-lg font-semibold mb-4">💻 代码编辑器</h3>
          <div class="border border-gray-200 rounded-lg h-96">
            <CodeEditor
              v-if="selectedFile"
              :file-path="selectedFile"
              :content="fileContent"
              @content-change="handleContentChange"
              @save="handleFileSave"
            />
            <div v-else class="h-full flex items-center justify-center text-gray-500">
              请选择一个文件查看内容
            </div>
          </div>
        </div>
      </div>
      
      <!-- 事件日志 -->
      <div class="mt-6 bg-white rounded-lg shadow p-4">
        <h3 class="text-lg font-semibold mb-4">📝 事件日志</h3>
        <div class="max-h-64 overflow-y-auto bg-gray-50 p-4 rounded">
          <div 
            v-for="(log, index) in eventLogs" 
            :key="index"
            class="text-sm mb-1"
            :class="getLogClass(log.type)"
          >
            <span class="text-gray-500">{{ log.timestamp }}</span>
            <span class="ml-2 font-medium">{{ log.type }}</span>
            <span class="ml-2">{{ log.message }}</span>
          </div>
          <div v-if="eventLogs.length === 0" class="text-gray-500 text-center">
            暂无事件日志
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import FileExplorer from '@/components/FileExplorer.vue'
import CodeEditor from '@/components/CodeEditor.vue'
import { startHybridGeneration, shouldUseHybridMode, extractFilePathFromMessage, inferFileTypeFromPath } from '@/services/hybridGenerationApi'
import { useSSEManager } from '@/services/sseManager'

// 响应式数据
const selectedMessage = ref('创建一个HelloWorld.java文件')
const customMessage = ref('')
const isLoading = ref(false)
const status = ref('')
const statusType = ref<'success' | 'error' | 'info'>('info')
const selectedFile = ref('')
const fileContent = ref('')
const eventLogs = ref<Array<{timestamp: string, type: string, message: string}>>([])
const currentTaskId = ref<string>()

// 组件引用
const fileExplorerRef = ref()

// SSE管理器
const sseManager = useSSEManager()

// 计算属性
const statusClass = computed(() => ({
  'bg-green-100 text-green-800': statusType.value === 'success',
  'bg-red-100 text-red-800': statusType.value === 'error',
  'bg-blue-100 text-blue-800': statusType.value === 'info',
}))

// 方法
const addLog = (type: string, message: string) => {
  eventLogs.value.push({
    timestamp: new Date().toLocaleTimeString(),
    type,
    message
  })
}

const setStatus = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  status.value = message
  statusType.value = type
}

const getLogClass = (type: string) => {
  if (type.includes('ERROR')) return 'text-red-600'
  if (type.includes('SUCCESS') || type.includes('COMPLETE')) return 'text-green-600'
  if (type.includes('START')) return 'text-blue-600'
  return 'text-gray-600'
}

const sendTestMessage = async () => {
  const message = customMessage.value.trim() || selectedMessage.value
  if (!message || isLoading.value) return

  isLoading.value = true
  
  try {
    addLog('TEST_START', `发送测试消息: ${message}`)
    
    // 判断是否使用混合模式
    const useHybridMode = shouldUseHybridMode(message)
    addLog('MODE_DETECTION', `模式判断: ${useHybridMode ? '混合模式' : '普通模式'}`)
    
    if (useHybridMode) {
      // 提取文件路径
      const extractedPath = extractFilePathFromMessage(message)
      if (extractedPath) {
        addLog('PATH_EXTRACTED', `提取文件路径: ${extractedPath}`)
        
        // 推断文件类型
        const fileType = inferFileTypeFromPath(extractedPath)
        addLog('TYPE_INFERRED', `推断文件类型: ${fileType || '未知'}`)

        // 使用混合模式
        const hybridResponse = await startHybridGeneration({
          message: message,
          filePath: extractedPath,
          fileType: fileType
        })

        if (hybridResponse.success && hybridResponse.taskId) {
          currentTaskId.value = hybridResponse.taskId
          addLog('HYBRID_STARTED', `混合模式任务启动: ${hybridResponse.taskId}`)
          setStatus('混合模式任务已启动，正在建立SSE连接...', 'info')
          
          // 建立SSE连接
          await startSSEConnection(hybridResponse.taskId)
        } else {
          throw new Error(hybridResponse.error || '启动混合模式失败')
        }
      } else {
        addLog('PATH_ERROR', '无法从消息中提取文件路径')
        setStatus('无法从消息中提取文件路径', 'error')
      }
    } else {
      addLog('MODE_ERROR', '消息不符合混合模式条件')
      setStatus('消息不符合混合模式条件，请包含文件创建意图', 'error')
    }
    
  } catch (error) {
    console.error('测试失败:', error)
    setStatus(`测试失败: ${error instanceof Error ? error.message : '未知错误'}`, 'error')
    addLog('TEST_ERROR', `测试失败: ${error instanceof Error ? error.message : '未知错误'}`)
  } finally {
    isLoading.value = false
  }
}

const startSSEConnection = async (taskId: string) => {
  try {
    await sseManager.startLogStream(taskId, {
      onOpen: () => {
        addLog('SSE_CONNECTED', 'SSE连接建立成功')
        setStatus('SSE连接已建立，等待事件...', 'info')
      },
      onMessage: (event) => {
        handleSSEEvent(event)
      },
      onError: (error) => {
        addLog('SSE_ERROR', `SSE连接错误: ${error}`)
        setStatus('SSE连接错误', 'error')
      },
      onClose: () => {
        addLog('SSE_CLOSED', 'SSE连接关闭')
        isLoading.value = false
      }
    })
  } catch (error) {
    addLog('SSE_START_ERROR', `启动SSE连接失败: ${error}`)
    setStatus('启动SSE连接失败', 'error')
    isLoading.value = false
  }
}

const handleSSEEvent = (event: any) => {
  addLog(event.type, event.message || '收到事件')
  
  switch (event.type) {
    case 'CODE_GENERATION_START':
      setStatus('开始生成代码...', 'info')
      // 刷新文件浏览器
      if (fileExplorerRef.value) {
        fileExplorerRef.value.refresh()
      }
      break
      
    case 'CODE_CHUNK':
      setStatus(`正在生成代码... (收到 ${event.details?.chunkLength || 0} 字符)`, 'info')
      break
      
    case 'CODE_GENERATION_COMPLETE':
      setStatus('代码生成完成！', 'success')
      isLoading.value = false
      break
      
    case 'CODE_GENERATION_ERROR':
      setStatus(`代码生成失败: ${event.details?.error || '未知错误'}`, 'error')
      isLoading.value = false
      break
      
    case 'TASK_COMPLETE':
      setStatus('任务完成！', 'success')
      isLoading.value = false
      break
  }
}

const handleFileSelected = (filePath: string, content: string) => {
  selectedFile.value = filePath
  fileContent.value = content
  addLog('FILE_SELECTED', `选择文件: ${filePath}`)
}

const handleContentChange = (content: string) => {
  fileContent.value = content
}

const handleFileSave = (filePath: string, content: string) => {
  addLog('FILE_SAVED', `保存文件: ${filePath}`)
}

const handleRefresh = () => {
  addLog('EXPLORER_REFRESH', '文件浏览器刷新')
}

const clearTest = () => {
  eventLogs.value = []
  status.value = ''
  selectedFile.value = ''
  fileContent.value = ''
  isLoading.value = false
  
  if (currentTaskId.value) {
    sseManager.closeConnection(currentTaskId.value)
    currentTaskId.value = undefined
  }
  
  addLog('TEST_CLEARED', '测试数据已清除')
}
</script>

<style scoped>
.typewriter-real-test-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}
</style>
