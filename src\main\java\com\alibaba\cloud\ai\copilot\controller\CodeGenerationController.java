package com.alibaba.cloud.ai.copilot.controller;

import com.alibaba.cloud.ai.copilot.service.StreamingCodeGenerationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 代码生成控制器
 * 处理独立的代码生成请求，不依赖MCP工具
 */
@RestController
@RequestMapping("/api/code-generation")
@CrossOrigin(origins = "*")
public class CodeGenerationController {

    private static final Logger logger = LoggerFactory.getLogger(CodeGenerationController.class);

    @Autowired
    private StreamingCodeGenerationService codeGenerationService;

    /**
     * 启动代码生成任务
     */
    @PostMapping("/start")
    public ResponseEntity<CodeGenerationResponse> startCodeGeneration(@RequestBody CodeGenerationRequest request) {
        try {
            logger.info("🚀 启动代码生成任务: filePath={}, prompt={}", request.getFilePath(), request.getPrompt());

            // 验证请求参数
            if (request.getTaskId() == null || request.getTaskId().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(
                    CodeGenerationResponse.error("Task ID is required")
                );
            }

            if (request.getFilePath() == null || request.getFilePath().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(
                    CodeGenerationResponse.error("File path is required")
                );
            }

            if (request.getPrompt() == null || request.getPrompt().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(
                    CodeGenerationResponse.error("Prompt is required")
                );
            }

            // 启动代码生成
            String generationId = codeGenerationService.startCodeGeneration(
                request.getTaskId(),
                request.getFilePath(),
                request.getPrompt(),
                request.getFileType()
            );

            logger.info("✅ 代码生成任务已启动: generationId={}", generationId);

            return ResponseEntity.ok(CodeGenerationResponse.success(
                "Code generation started successfully",
                generationId
            ));

        } catch (Exception e) {
            logger.error("❌ 启动代码生成任务失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(
                CodeGenerationResponse.error("Failed to start code generation: " + e.getMessage())
            );
        }
    }

    /**
     * 停止代码生成任务
     */
    @PostMapping("/stop/{generationId}")
    public ResponseEntity<CodeGenerationResponse> stopCodeGeneration(@PathVariable String generationId) {
        try {
            logger.info("🛑 停止代码生成任务: generationId={}", generationId);

            boolean stopped = codeGenerationService.stopCodeGeneration(generationId);

            if (stopped) {
                logger.info("✅ 代码生成任务已停止: generationId={}", generationId);
                return ResponseEntity.ok(CodeGenerationResponse.success(
                    "Code generation stopped successfully",
                    generationId
                ));
            } else {
                logger.warn("⚠️ 代码生成任务不存在或已完成: generationId={}", generationId);
                return ResponseEntity.ok(CodeGenerationResponse.success(
                    "Code generation task not found or already completed",
                    generationId
                ));
            }

        } catch (Exception e) {
            logger.error("❌ 停止代码生成任务失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(
                CodeGenerationResponse.error("Failed to stop code generation: " + e.getMessage())
            );
        }
    }

    /**
     * 获取活跃任务状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        try {
            int activeTaskCount = codeGenerationService.getActiveTaskCount();
            
            return ResponseEntity.ok(Map.of(
                "activeTaskCount", activeTaskCount,
                "status", "running"
            ));

        } catch (Exception e) {
            logger.error("❌ 获取代码生成状态失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(Map.of(
                "error", e.getMessage(),
                "status", "error"
            ));
        }
    }

    /**
     * 代码生成请求DTO
     */
    public static class CodeGenerationRequest {
        private String taskId;
        private String filePath;
        private String prompt;
        private String fileType;

        // 构造器
        public CodeGenerationRequest() {}

        public CodeGenerationRequest(String taskId, String filePath, String prompt, String fileType) {
            this.taskId = taskId;
            this.filePath = filePath;
            this.prompt = prompt;
            this.fileType = fileType;
        }

        // Getters and Setters
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }

        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }

        public String getPrompt() { return prompt; }
        public void setPrompt(String prompt) { this.prompt = prompt; }

        public String getFileType() { return fileType; }
        public void setFileType(String fileType) { this.fileType = fileType; }

        @Override
        public String toString() {
            return String.format("CodeGenerationRequest{taskId='%s', filePath='%s', fileType='%s', promptLength=%d}",
                taskId, filePath, fileType, prompt != null ? prompt.length() : 0);
        }
    }

    /**
     * 代码生成响应DTO
     */
    public static class CodeGenerationResponse {
        private boolean success;
        private String message;
        private String generationId;
        private String error;

        // 构造器
        public CodeGenerationResponse() {}

        public CodeGenerationResponse(boolean success, String message, String generationId, String error) {
            this.success = success;
            this.message = message;
            this.generationId = generationId;
            this.error = error;
        }

        // 静态工厂方法
        public static CodeGenerationResponse success(String message, String generationId) {
            return new CodeGenerationResponse(true, message, generationId, null);
        }

        public static CodeGenerationResponse error(String error) {
            return new CodeGenerationResponse(false, null, null, error);
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public String getGenerationId() { return generationId; }
        public void setGenerationId(String generationId) { this.generationId = generationId; }

        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
    }
}
