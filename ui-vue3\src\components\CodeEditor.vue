<template>
  <div class="code-editor h-full flex flex-col bg-white dark:bg-gray-900">
    <!-- 编辑器工具栏 -->
    <div class="editor-toolbar flex items-center justify-between px-4 py-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-2">
        <span class="text-sm text-gray-600 dark:text-gray-300">
          {{ filePath }}
        </span>
        <span v-if="isDirty" class="text-xs text-orange-500">●</span>
        <span v-if="isStreaming" class="text-xs text-blue-500 animate-pulse">
          🎯 正在流式生成...
        </span>
      </div>

      <div class="flex items-center space-x-2">
        <a-button size="small" @click="formatCode" :disabled="!canFormat">
          格式化
        </a-button>
        <a-button size="small" @click="saveFile" :disabled="!isDirty">
          保存
        </a-button>
      </div>
    </div>

    <!-- 编辑器容器 -->
    <div class="editor-container flex-1 relative">
      <textarea
        ref="editorRef"
        v-model="editorContent"
        class="h-full w-full p-4 border-none outline-none resize-none bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 font-mono text-sm"
        :class="{ 'streaming-cursor': isStreaming }"
        :placeholder="`开始编辑 ${filePath}...`"
        @input="handleInput"
        @keydown="handleKeyDown"
        spellcheck="false"
        :readonly="isStreaming"
      />

      <!-- 流式生成指示器 -->
      <div
        v-if="isStreaming"
        class="absolute bottom-4 right-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs animate-pulse"
      >
        🎯 AI正在生成代码...
      </div>

      <!-- 加载状态 -->
      <div
        v-if="loading"
        class="absolute inset-0 flex items-center justify-center bg-white dark:bg-gray-900 bg-opacity-75"
      >
        <a-spin size="large" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { useWebContainerStore } from '@/stores/webContainerStore'

interface Props {
  filePath: string
  content: string
}

interface Emits {
  (e: 'content-change', content: string): void
  (e: 'save', filePath: string, content: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const webContainerStore = useWebContainerStore()

// 状态
const editorRef = ref<HTMLTextAreaElement>()
const loading = ref(false)
const isDirty = ref(false)
const editorContent = ref('')
const isStreaming = ref(false)

// 检查是否可以格式化
const canFormat = ref(true)

// 处理输入
const handleInput = () => {
  isDirty.value = editorContent.value !== props.content
  emit('content-change', editorContent.value)
}

// 格式化代码
const formatCode = () => {
  try {
    const extension = props.filePath.split('.').pop()?.toLowerCase()

    // 简单的格式化逻辑
    if (extension === 'json') {
      try {
        const formatted = JSON.stringify(JSON.parse(editorContent.value), null, 2)
        editorContent.value = formatted
        handleInput()
      } catch (e) {
        console.warn('Invalid JSON, cannot format')
      }
    }
  } catch (error) {
    console.error('Failed to format code:', error)
  }
}

// 保存文件
const saveFile = async () => {
  if (!isDirty.value) return

  try {
    // 如果 WebContainer 已初始化，同步到容器
    if (webContainerStore.isReady) {
      await webContainerStore.writeFile(props.filePath, editorContent.value)
    }

    emit('save', props.filePath, editorContent.value)
    isDirty.value = false
  } catch (error) {
    console.error('Failed to save file:', error)
  }
}

// 键盘快捷键处理
const handleKeyDown = (event: KeyboardEvent) => {
  // Ctrl+S 保存
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault()
    saveFile()
  }

  // Ctrl+Shift+F 格式化
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'F') {
    event.preventDefault()
    formatCode()
  }
}

// 打字机效果事件处理
const handleTypewriterChunk = (event: CustomEvent) => {
  const { filePath, chunk, isStreaming: streaming } = event.detail

  // 只处理当前文件的事件
  if (filePath === props.filePath) {
    console.log('🎯 CodeEditor收到打字机事件:', { filePath, chunkLength: chunk.length, streaming })

    isStreaming.value = streaming

    // 逐字符添加内容，实现打字机效果
    addChunkWithTypewriterEffect(chunk)
  }
}

// 打字机效果添加内容
const addChunkWithTypewriterEffect = async (chunk: string) => {
  // 如果不是流式模式，直接添加
  if (!isStreaming.value) {
    editorContent.value += chunk
    return
  }

  // 逐字符添加，创建打字机效果
  for (let i = 0; i < chunk.length; i++) {
    if (!isStreaming.value) break // 如果流式模式被取消，停止

    editorContent.value += chunk[i]

    // 滚动到底部
    if (editorRef.value) {
      editorRef.value.scrollTop = editorRef.value.scrollHeight
    }

    // 控制打字机速度
    await new Promise(resolve => setTimeout(resolve, 20))
  }
}

// 生命周期钩子
onMounted(() => {
  // 监听打字机效果事件
  window.addEventListener('typewriter-code-chunk', handleTypewriterChunk as EventListener)

  // 监听代码生成开始事件
  window.addEventListener('typewriter-generation-start', (event: CustomEvent) => {
    const { filePath } = event.detail
    if (filePath === props.filePath) {
      console.log('🚀 CodeEditor: 代码生成开始', filePath)
      isStreaming.value = true
      editorContent.value = '' // 清空内容准备接收新内容
    }
  })

  // 监听代码生成完成事件
  window.addEventListener('typewriter-generation-complete', (event: CustomEvent) => {
    const { filePath } = event.detail
    if (filePath === props.filePath) {
      console.log('✅ CodeEditor: 代码生成完成', filePath)
      isStreaming.value = false
    }
  })
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('typewriter-code-chunk', handleTypewriterChunk as EventListener)
})

// 监听内容变化
watch(() => props.content, (newContent) => {
  // 如果不是流式模式，正常更新内容
  if (!isStreaming.value && editorContent.value !== newContent) {
    editorContent.value = newContent
    isDirty.value = false
  }
}, { immediate: true })
</script>

<style scoped>
.code-editor {
  font-family: 'Fira Code', 'JetBrains Mono', 'Monaco', 'Consolas', monospace;
}

.editor-container {
  overflow: hidden;
}

textarea {
  font-family: 'Fira Code', 'JetBrains Mono', 'Monaco', 'Consolas', monospace;
  line-height: 1.5;
  tab-size: 2;
}

textarea:focus {
  outline: none;
  box-shadow: none;
}

/* 流式生成时的特殊样式 */
.streaming-cursor {
  caret-color: transparent;
}

.streaming-cursor::after {
  content: '|';
  color: #3b82f6;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
</style>
