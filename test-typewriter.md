# 打字机效果测试指南

## 快速测试步骤

### 1. 启动后端服务
```bash
# 在项目根目录执行
mvn spring-boot:run
```

### 2. 启动前端服务
```bash
# 进入前端目录
cd ui-vue3

# 安装依赖（如果还没有安装）
npm install

# 启动开发服务器
npm run dev
```

### 3. 访问测试页面
打开浏览器访问：`http://localhost:5173/typewriter-test`

### 4. 测试用例

#### 测试用例1：Java类生成
- **文件路径**：`workspace/test/HelloWorld.java`
- **文件类型**：`java`
- **提示**：`创建一个简单的Hello World Java类，包含main方法`

#### 测试用例2：JavaScript函数
- **文件路径**：`workspace/test/utils.js`
- **文件类型**：`javascript`
- **提示**：`创建一个包含常用工具函数的JavaScript文件`

#### 测试用例3：TypeScript组件
- **文件路径**：`workspace/test/Button.tsx`
- **文件类型**：`typescript`
- **提示**：`创建一个React Button组件，支持不同的样式和点击事件`

### 5. 观察效果

在测试页面中，你应该能看到：

1. **状态变化**：
   - "混合模式任务已启动"
   - "SSE连接已建立"
   - "开始生成代码..."
   - "正在生成代码... (X 字符)"
   - "代码生成完成！"

2. **实时代码显示**：
   - 代码区域逐字符显示生成的代码
   - 字符计数实时更新

3. **事件日志**：
   - 详细的事件时间线
   - 不同类型事件的颜色标识

### 6. 在主界面测试

回到主编辑器界面（`http://localhost:5173/editor`），尝试发送以下消息：

- `创建一个HelloWorld.java文件`
- `生成一个简单的Python脚本 test.py`
- `写一个JavaScript工具类 utils.js`

系统会自动判断是否使用混合模式，并展示打字机效果。

## 预期行为

### 正常流程
1. 用户发送包含文件创建意图的消息
2. 系统判断使用混合模式
3. MCP工具创建空白文件
4. 文件浏览器显示新文件
5. 独立代码生成服务开始工作
6. 编辑器区域逐字符显示代码
7. 生成完成后保存文件

### 错误处理
- 文件路径无效：显示错误信息
- 代码生成失败：显示具体错误
- SSE连接断开：自动重连或显示连接状态

## 调试技巧

### 1. 查看浏览器控制台
- 网络请求状态
- SSE连接状态
- JavaScript错误信息

### 2. 查看后端日志
- Spring Boot控制台输出
- 工具调用日志
- SSE推送日志

### 3. 检查文件系统
- 确认workspace目录存在
- 检查文件是否正确创建
- 验证文件内容

## 常见问题

### Q: 代码生成没有开始
**A**: 检查：
- 文件路径是否正确
- 是否触发了混合模式判断
- SSE连接是否建立成功

### Q: 打字机效果不流畅
**A**: 可能原因：
- 网络延迟
- 代码生成速度过快
- 前端渲染性能问题

### Q: 文件没有创建
**A**: 检查：
- workspace目录权限
- 文件路径格式
- 文件扩展名是否在允许列表中

## 性能优化建议

1. **调整块大小**：在StreamingCodeGenerationService中调整分块大小
2. **延迟控制**：添加适当的延迟来控制打字机速度
3. **缓存优化**：对频繁访问的数据进行缓存
4. **连接管理**：及时清理无用的SSE连接

## 扩展功能

基于当前实现，可以考虑添加：

1. **速度控制**：用户可调节打字机速度
2. **暂停/继续**：允许用户暂停代码生成
3. **语法高亮**：实时语法高亮显示
4. **多文件支持**：同时生成多个相关文件
5. **模板选择**：预定义的代码模板

## 反馈和改进

如果在测试过程中发现问题或有改进建议，请记录：

1. **问题描述**：详细描述遇到的问题
2. **重现步骤**：如何重现该问题
3. **期望行为**：期望的正确行为
4. **环境信息**：浏览器版本、操作系统等

这将帮助我们持续改进打字机效果的实现。
