<template>
  <div class="typewriter-test-container p-6">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-2xl font-bold mb-6">打字机效果测试</h1>
      
      <!-- 测试控制面板 -->
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-lg font-semibold mb-4">测试控制</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label class="block text-sm font-medium mb-2">文件路径</label>
            <input 
              v-model="testFilePath" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="例如: workspace/test/HelloWorld.java"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-2">文件类型</label>
            <select v-model="testFileType" class="w-full px-3 py-2 border border-gray-300 rounded-md">
              <option value="">自动推断</option>
              <option value="java">Java</option>
              <option value="javascript">JavaScript</option>
              <option value="typescript">TypeScript</option>
              <option value="python">Python</option>
            </select>
          </div>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-2">代码生成提示</label>
          <textarea 
            v-model="testPrompt" 
            rows="3" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md"
            placeholder="例如: 创建一个简单的Hello World Java类"
          ></textarea>
        </div>
        
        <div class="flex space-x-4">
          <button 
            @click="startTest" 
            :disabled="isLoading"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
          >
            {{ isLoading ? '生成中...' : '开始测试' }}
          </button>
          
          <button 
            @click="clearTest" 
            class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
          >
            清除
          </button>
        </div>
      </div>
      
      <!-- 状态显示 -->
      <div v-if="status" class="mb-6">
        <div :class="statusClass" class="px-4 py-2 rounded-md">
          {{ status }}
        </div>
      </div>
      
      <!-- 实时代码显示 -->
      <div class="bg-gray-900 rounded-lg p-4 mb-6">
        <div class="flex justify-between items-center mb-2">
          <h3 class="text-white font-semibold">实时代码生成</h3>
          <div class="text-gray-400 text-sm">
            {{ generatedCode.length }} 字符
          </div>
        </div>
        
        <pre class="text-green-400 font-mono text-sm whitespace-pre-wrap">{{ generatedCode || '等待代码生成...' }}</pre>
      </div>
      
      <!-- 事件日志 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold mb-4">事件日志</h3>
        
        <div class="max-h-96 overflow-y-auto">
          <div 
            v-for="(log, index) in eventLogs" 
            :key="index"
            class="mb-2 p-2 bg-gray-50 rounded text-sm"
          >
            <span class="text-gray-500">{{ log.timestamp }}</span>
            <span :class="getLogTypeClass(log.type)" class="ml-2 font-semibold">{{ log.type }}</span>
            <span class="ml-2">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { startHybridGeneration, inferFileTypeFromPath } from '../services/hybridGenerationApi'
import { useSSEManager } from '../services/sseManager'

// 响应式数据
const testFilePath = ref('workspace/test/HelloWorld.java')
const testFileType = ref('')
const testPrompt = ref('创建一个简单的Hello World Java类，包含main方法')
const isLoading = ref(false)
const status = ref('')
const statusType = ref<'success' | 'error' | 'info'>('info')
const generatedCode = ref('')
const eventLogs = ref<Array<{timestamp: string, type: string, message: string}>>([])
const currentTaskId = ref<string>()

// SSE管理器
const sseManager = useSSEManager()

// 计算属性
const statusClass = computed(() => {
  return {
    'bg-green-100 text-green-800': statusType.value === 'success',
    'bg-red-100 text-red-800': statusType.value === 'error',
    'bg-blue-100 text-blue-800': statusType.value === 'info',
  }
})

// 方法
const startTest = async () => {
  if (!testFilePath.value.trim() || !testPrompt.value.trim()) {
    setStatus('请填写文件路径和提示', 'error')
    return
  }

  isLoading.value = true
  generatedCode.value = ''
  eventLogs.value = []
  
  try {
    addLog('TEST_START', '开始混合模式测试')
    
    // 推断文件类型
    const fileType = testFileType.value || inferFileTypeFromPath(testFilePath.value)
    addLog('FILE_TYPE_INFERRED', `推断文件类型: ${fileType || '未知'}`)
    
    // 启动混合模式生成
    const response = await startHybridGeneration({
      message: testPrompt.value,
      filePath: testFilePath.value,
      fileType: fileType
    })
    
    if (response.success && response.taskId) {
      currentTaskId.value = response.taskId
      addLog('HYBRID_MODE_STARTED', `混合模式任务启动: ${response.taskId}`)
      setStatus('混合模式任务已启动，正在建立SSE连接...', 'info')
      
      // 建立SSE连接
      await startSSEConnection(response.taskId)
    } else {
      throw new Error(response.error || '启动混合模式失败')
    }
    
  } catch (error) {
    console.error('测试失败:', error)
    setStatus(`测试失败: ${error instanceof Error ? error.message : '未知错误'}`, 'error')
    addLog('TEST_ERROR', `测试失败: ${error instanceof Error ? error.message : '未知错误'}`)
    isLoading.value = false
  }
}

const clearTest = () => {
  generatedCode.value = ''
  eventLogs.value = []
  status.value = ''
  isLoading.value = false
  
  if (currentTaskId.value) {
    sseManager.closeConnection(currentTaskId.value)
    currentTaskId.value = undefined
  }
}

const setStatus = (message: string, type: 'success' | 'error' | 'info') => {
  status.value = message
  statusType.value = type
}

const addLog = (type: string, message: string) => {
  eventLogs.value.push({
    timestamp: new Date().toLocaleTimeString(),
    type,
    message
  })
}

const getLogTypeClass = (type: string) => {
  if (type.includes('ERROR')) return 'text-red-600'
  if (type.includes('SUCCESS') || type.includes('COMPLETE')) return 'text-green-600'
  if (type.includes('START')) return 'text-blue-600'
  return 'text-gray-600'
}

const startSSEConnection = async (taskId: string) => {
  try {
    await sseManager.startLogStream(taskId, {
      onOpen: () => {
        addLog('SSE_CONNECTED', 'SSE连接建立成功')
        setStatus('SSE连接已建立，等待事件...', 'info')
      },
      onMessage: (event) => {
        handleSSEEvent(event)
      },
      onError: (error) => {
        addLog('SSE_ERROR', `SSE连接错误: ${error}`)
        setStatus('SSE连接错误', 'error')
      },
      onClose: () => {
        addLog('SSE_CLOSED', 'SSE连接关闭')
        isLoading.value = false
      }
    })
  } catch (error) {
    addLog('SSE_START_ERROR', `启动SSE连接失败: ${error}`)
    setStatus('启动SSE连接失败', 'error')
    isLoading.value = false
  }
}

const handleSSEEvent = (event: any) => {
  addLog(event.type, event.message || '收到事件')
  
  switch (event.type) {
    case 'CODE_GENERATION_START':
      setStatus('开始生成代码...', 'info')
      break
      
    case 'CODE_CHUNK':
      // 模拟打字机效果
      const chunk = event.details?.chunk || ''
      generatedCode.value += chunk
      setStatus(`正在生成代码... (${generatedCode.value.length} 字符)`, 'info')
      break
      
    case 'CODE_GENERATION_COMPLETE':
      setStatus('代码生成完成！', 'success')
      isLoading.value = false
      break
      
    case 'CODE_GENERATION_ERROR':
      setStatus(`代码生成失败: ${event.details?.error || '未知错误'}`, 'error')
      isLoading.value = false
      break
      
    case 'TASK_COMPLETE':
      setStatus('任务完成！', 'success')
      isLoading.value = false
      break
  }
}
</script>

<style scoped>
.typewriter-test-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}
</style>
