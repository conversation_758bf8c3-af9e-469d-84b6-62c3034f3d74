package com.alibaba.cloud.ai.copilot.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 混合模式服务
 * 协调MCP工具创建空文件和独立代码生成的完整流程
 */
@Service
public class HybridModeService {

    private static final Logger logger = LoggerFactory.getLogger(HybridModeService.class);

    @Autowired
    private ChatClient chatClient;
    
    @Autowired
    private StreamingCodeGenerationService codeGenerationService;
    
    @Autowired
    private LogStreamService logStreamService;

    // 活跃的混合模式任务
    private final Map<String, HybridTask> activeTasks = new ConcurrentHashMap<>();

    /**
     * 启动混合模式任务
     */
    public String startHybridTask(String taskId, String filePath, String message, String fileType) {
        String hybridId = UUID.randomUUID().toString();
        
        logger.info("🚀 启动混合模式任务: taskId={}, filePath={}, hybridId={}", taskId, filePath, hybridId);

        HybridTask task = new HybridTask(hybridId, taskId, filePath, message, fileType);
        activeTasks.put(hybridId, task);

        // 异步执行混合模式流程
        CompletableFuture.runAsync(() -> executeHybridTask(task));

        return hybridId;
    }

    /**
     * 执行混合模式任务
     */
    private void executeHybridTask(HybridTask task) {
        try {
            logger.info("📝 开始执行混合模式任务: {}", task.getHybridId());

            // 阶段1: 创建空文件
            executeFileCreationPhase(task);

            // 等待文件创建完成
            Thread.sleep(1000);

            // 阶段2: 启动代码生成
            executeCodeGenerationPhase(task);

        } catch (Exception e) {
            logger.error("❌ 混合模式任务执行失败: {}", e.getMessage(), e);
            handleTaskError(task, e);
        }
    }

    /**
     * 执行文件创建阶段
     */
    private void executeFileCreationPhase(HybridTask task) {
        try {
            logger.info("📁 阶段1: 创建空文件 - {}", task.getFilePath());

            // 推送阶段开始事件
            logStreamService.pushHybridPhaseStart(task.getTaskId(), "FILE_CREATION", "开始创建空文件");

            // 构建创建空文件的提示
            String createFilePrompt = String.format(
                "Use the create_empty_file tool to create an empty file at path: %s. " +
                "File type: %s. This is the first phase of a hybrid generation process.",
                task.getFilePath(),
                task.getFileType() != null ? task.getFileType() : "unknown"
            );

            // 执行MCP工具
            String result = chatClient.prompt()
                .user(createFilePrompt)
                .call()
                .content();

            logger.info("📁 文件创建结果: {}", result);

            // 推送阶段完成事件
            logStreamService.pushHybridPhaseComplete(task.getTaskId(), "FILE_CREATION", "空文件创建完成");

        } catch (Exception e) {
            logger.error("❌ 文件创建阶段失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件创建失败: " + e.getMessage());
        }
    }

    /**
     * 执行代码生成阶段
     */
    private void executeCodeGenerationPhase(HybridTask task) {
        try {
            logger.info("💻 阶段2: 开始代码生成 - {}", task.getFilePath());

            // 推送阶段开始事件
            logStreamService.pushHybridPhaseStart(task.getTaskId(), "CODE_GENERATION", "开始流式代码生成");

            // 启动代码生成，并设置完成回调
            String generationId = codeGenerationService.startCodeGeneration(
                task.getTaskId(),
                task.getFilePath(),
                task.getMessage(),
                task.getFileType()
            );

            task.setGenerationId(generationId);
            logger.info("💻 代码生成已启动: generationId={}", generationId);

        } catch (Exception e) {
            logger.error("❌ 代码生成阶段失败: {}", e.getMessage(), e);
            throw new RuntimeException("代码生成失败: " + e.getMessage());
        }
    }

    /**
     * 处理任务错误
     */
    private void handleTaskError(HybridTask task, Exception error) {
        logger.error("❌ 混合模式任务错误: taskId={}, error={}", task.getTaskId(), error.getMessage());
        
        // 推送错误事件
        logStreamService.pushHybridError(task.getTaskId(), error.getMessage());
        
        // 清理任务
        activeTasks.remove(task.getHybridId());
    }

    /**
     * 完成混合模式任务
     * 由StreamingCodeGenerationService在代码生成完成后调用
     */
    public void completeHybridTask(String taskId) {
        // 查找对应的混合任务
        HybridTask task = activeTasks.values().stream()
            .filter(t -> t.getTaskId().equals(taskId))
            .findFirst()
            .orElse(null);

        if (task != null) {
            logger.info("✅ 混合模式任务完成: taskId={}, hybridId={}", taskId, task.getHybridId());
            
            // 推送最终完成事件
            logStreamService.pushTaskComplete(taskId);
            
            // 清理任务
            activeTasks.remove(task.getHybridId());
        }
    }

    /**
     * 获取活跃任务数量
     */
    public int getActiveTaskCount() {
        return activeTasks.size();
    }

    /**
     * 混合模式任务
     */
    private static class HybridTask {
        private final String hybridId;
        private final String taskId;
        private final String filePath;
        private final String message;
        private final String fileType;
        private final long startTime;
        private String generationId;

        public HybridTask(String hybridId, String taskId, String filePath, String message, String fileType) {
            this.hybridId = hybridId;
            this.taskId = taskId;
            this.filePath = filePath;
            this.message = message;
            this.fileType = fileType;
            this.startTime = System.currentTimeMillis();
        }

        // Getters and Setters
        public String getHybridId() { return hybridId; }
        public String getTaskId() { return taskId; }
        public String getFilePath() { return filePath; }
        public String getMessage() { return message; }
        public String getFileType() { return fileType; }
        public long getStartTime() { return startTime; }
        public String getGenerationId() { return generationId; }
        public void setGenerationId(String generationId) { this.generationId = generationId; }
    }
}
