import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/editor',
    name: 'Editor',
    component: () => import('../views/Editor.vue')
  },
  {
    path: '/typewriter-test',
    name: 'TypewriterTest',
    component: () => import('../views/TypewriterTest.vue')
  },
  {
    path: '/streaming-demo',
    name: 'StreamingDemo',
    component: () => import('../views/StreamingDemo.vue')
  },
  {
    path: '/typewriter-real-test',
    name: 'TypewriterRealTest',
    component: () => import('../views/TypewriterRealTest.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
