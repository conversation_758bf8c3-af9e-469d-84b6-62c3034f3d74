package com.alibaba.cloud.ai.copilot.tools;

import com.alibaba.cloud.ai.copilot.config.AppProperties;
import com.alibaba.cloud.ai.copilot.config.TaskContextHolder;
import com.alibaba.cloud.ai.copilot.schema.JsonSchema;
import com.alibaba.cloud.ai.copilot.service.ToolExecutionLogger;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 创建空白文件工具
 * 只负责创建空白文件，不写入内容，为后续的流式代码生成做准备
 */
@Component
public class CreateEmptyFileTool extends BaseTool<CreateEmptyFileTool.CreateEmptyFileParams> {

    private final String rootDirectory;
    private final AppProperties appProperties;

    @Autowired
    private ToolExecutionLogger executionLogger;

    public CreateEmptyFileTool(AppProperties appProperties) {
        super(
            "create_empty_file",
            "CreateEmptyFile",
            "Creates an empty file at the specified path. " +
            "Use this when you want to create a file structure first, " +
            "then fill it with content using a separate streaming process. " +
            "The file will be created empty and ready for content streaming.",
            createSchema()
        );
        this.appProperties = appProperties;
        this.rootDirectory = appProperties.getWorkspace().getRootDirectory();
    }

    private static String getWorkspaceBasePath() {
        return Paths.get(System.getProperty("user.dir"), "workspace").toString();
    }

    private static String getPathExample(String subPath) {
        return "Example: \"" + Paths.get(getWorkspaceBasePath(), subPath).toString() + "\"";
    }

    private static JsonSchema createSchema() {
        return JsonSchema.object()
            .addProperty("file_path", JsonSchema.string(
                "MUST be an absolute path to the file to create. Path must be within the workspace directory (" +
                getWorkspaceBasePath() + "). " +
                getPathExample("project/src/main.java") + ". " +
                "Relative paths are NOT allowed."
            ))
            .addProperty("file_type", JsonSchema.string(
                "Optional file type hint (e.g., 'java', 'javascript', 'python'). " +
                "This helps the system prepare for the appropriate content generation."
            ))
            .required("file_path");
    }

    @Override
    public String validateToolParams(CreateEmptyFileParams params) {
        String baseValidation = super.validateToolParams(params);
        if (baseValidation != null) {
            return baseValidation;
        }

        // 验证路径
        if (params.filePath == null || params.filePath.trim().isEmpty()) {
            return "File path cannot be empty";
        }

        Path filePath = Paths.get(params.filePath);

        // 验证是否为绝对路径
        if (!filePath.isAbsolute()) {
            return "File path must be absolute: " + params.filePath;
        }

        // 验证是否在工作目录内
        if (!isWithinWorkspace(filePath)) {
            return "File path must be within the workspace directory (" + rootDirectory + "): " + params.filePath;
        }

        // 验证文件扩展名
        String fileName = filePath.getFileName().toString();
        if (!isAllowedFileType(fileName)) {
            return "File type not allowed: " + fileName +
                ". Allowed extensions: " + appProperties.getWorkspace().getAllowedExtensions();
        }

        return null;
    }

    /**
     * Create empty file tool method for Spring AI integration
     */
    @Tool(name = "create_empty_file", description = "Creates an empty file at the specified path for later content streaming")
    public String createEmptyFile(String filePath, String fileType) {
        String taskId = TaskContextHolder.getCurrentTaskId();
        
        long callId = executionLogger.logToolStart("create_empty_file", "创建空白文件",
            String.format("文件路径=%s, 文件类型=%s", filePath, fileType != null ? fileType : "未指定"));
        long startTime = System.currentTimeMillis();

        try {
            CreateEmptyFileParams params = new CreateEmptyFileParams();
            params.setFilePath(filePath);
            params.setFileType(fileType);

            executionLogger.logToolStep(callId, "create_empty_file", "参数验证", "验证文件路径和类型");

            // Validate parameters
            String validation = validateToolParams(params);
            if (validation != null) {
                long executionTime = System.currentTimeMillis() - startTime;
                executionLogger.logToolError(callId, "create_empty_file", "参数验证失败: " + validation, executionTime);
                return "Error: " + validation;
            }

            executionLogger.logFileOperation(callId, "创建空白文件", filePath, "准备创建空白文件");

            // Execute the file creation
            ToolResult result = execute(params).join();

            long executionTime = System.currentTimeMillis() - startTime;

            if (result.isSuccess()) {
                executionLogger.logToolSuccess(callId, "create_empty_file", "空白文件创建成功", executionTime);
                return result.getLlmContent();
            } else {
                executionLogger.logToolError(callId, "create_empty_file", result.getErrorMessage(), executionTime);
                return "Error: " + result.getErrorMessage();
            }

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            executionLogger.logToolError(callId, "create_empty_file", "工具执行异常: " + e.getMessage(), executionTime);
            logger.error("Error in create empty file tool", e);
            return "Error: " + e.getMessage();
        }
    }

    @Override
    public CompletableFuture<ToolResult> execute(CreateEmptyFileParams params) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Path filePath = Paths.get(params.filePath);
                
                // 确保父目录存在
                Files.createDirectories(filePath.getParent());
                
                // 创建空文件
                if (Files.exists(filePath)) {
                    // 如果文件已存在，清空内容
                    Files.writeString(filePath, "", StandardCharsets.UTF_8);
                } else {
                    // 创建新的空文件
                    Files.createFile(filePath);
                }

                // 生成结果
                String relativePath = getRelativePath(filePath);
                String successMessage = String.format("Successfully created empty file: %s", params.filePath);
                String displayMessage = String.format("Created empty file: %s (ready for content streaming)", relativePath);
                
                return ToolResult.success(successMessage, displayMessage);

            } catch (Exception e) {
                logger.error("Error creating empty file: " + params.filePath, e);
                return ToolResult.error("Error creating empty file: " + e.getMessage());
            }
        });
    }

    private boolean isWithinWorkspace(Path filePath) {
        try {
            Path workspaceRoot = Paths.get(rootDirectory).toRealPath();
            Path normalizedPath = filePath.normalize();
            return normalizedPath.startsWith(workspaceRoot.normalize());
        } catch (Exception e) {
            logger.warn("Could not resolve workspace path", e);
            return false;
        }
    }

    private boolean isAllowedFileType(String fileName) {
        List<String> allowedExtensions = appProperties.getWorkspace().getAllowedExtensions();
        return allowedExtensions.stream()
            .anyMatch(ext -> fileName.toLowerCase().endsWith(ext.toLowerCase()));
    }

    private String getRelativePath(Path filePath) {
        try {
            Path workspaceRoot = Paths.get(rootDirectory);
            return workspaceRoot.relativize(filePath).toString();
        } catch (Exception e) {
            return filePath.toString();
        }
    }

    /**
     * 创建空白文件参数
     */
    public static class CreateEmptyFileParams {
        @JsonProperty("file_path")
        private String filePath;

        @JsonProperty("file_type")
        private String fileType;

        // 构造器
        public CreateEmptyFileParams() {}

        public CreateEmptyFileParams(String filePath, String fileType) {
            this.filePath = filePath;
            this.fileType = fileType;
        }

        // Getters and Setters
        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }

        public String getFileType() { return fileType; }
        public void setFileType(String fileType) { this.fileType = fileType; }

        @Override
        public String toString() {
            return String.format("CreateEmptyFileParams{path='%s', type='%s'}", filePath, fileType);
        }
    }
}
