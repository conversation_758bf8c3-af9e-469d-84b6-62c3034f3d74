package com.alibaba.cloud.ai.copilot.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 流式代码生成服务
 * 独立的代码生成服务，不注入任何工具，专门负责生成代码内容并流式推送
 */
@Service
public class StreamingCodeGenerationService {

    private static final Logger logger = LoggerFactory.getLogger(StreamingCodeGenerationService.class);

    // 创建一个不包含工具的独立ChatClient
    private final ChatClient codeGenerationChatClient;
    
    @Autowired
    private LogStreamService logStreamService;

    // 活跃的代码生成任务
    private final Map<String, CodeGenerationTask> activeTasks = new ConcurrentHashMap<>();

    public StreamingCodeGenerationService(ChatModel chatModel) {
        // 创建一个专门用于代码生成的ChatClient，不注入任何工具
        this.codeGenerationChatClient = ChatClient.builder(chatModel)
            .defaultSystem("You are a code generation assistant. Generate clean, well-structured code based on the requirements. " +
                          "Only return the code content without any explanations or markdown formatting.")
            .build();
    }

    /**
     * 启动流式代码生成任务
     */
    public String startCodeGeneration(String taskId, String filePath, String prompt, String fileType) {
        String generationId = UUID.randomUUID().toString();
        
        logger.info("🚀 启动流式代码生成: taskId={}, filePath={}, generationId={}", taskId, filePath, generationId);

        CodeGenerationTask task = new CodeGenerationTask(generationId, taskId, filePath, prompt, fileType);
        activeTasks.put(generationId, task);

        // 异步执行代码生成
        CompletableFuture.runAsync(() -> executeCodeGeneration(task));

        return generationId;
    }

    /**
     * 执行代码生成
     */
    private void executeCodeGeneration(CodeGenerationTask task) {
        try {
            logger.info("📝 开始生成代码: {}", task.getGenerationId());

            // 构建代码生成提示
            String codePrompt = buildCodeGenerationPrompt(task.getPrompt(), task.getFileType(), task.getFilePath());
            
            // 通知开始生成
            logStreamService.pushCodeGenerationStart(task.getTaskId(), task.getFilePath(), task.getGenerationId());

            // 使用流式API生成代码
            Flux<String> contentStream = codeGenerationChatClient.prompt()
                .user(codePrompt)
                .stream()
                .content();

            StringBuilder fullContent = new StringBuilder();
            
            contentStream
                .doOnNext(chunk -> {
                    logger.debug("📨 代码片段: {}", chunk);
                    fullContent.append(chunk);
                    
                    // 推送代码片段到前端
                    logStreamService.pushCodeChunk(
                        task.getTaskId(), 
                        task.getFilePath(), 
                        task.getGenerationId(),
                        chunk,
                        fullContent.length()
                    );
                    
                    // 实时写入文件
                    appendToFile(task.getFilePath(), chunk);
                })
                .doOnComplete(() -> {
                    logger.info("✅ 代码生成完成: {}", task.getGenerationId());
                    
                    // 通知生成完成
                    logStreamService.pushCodeGenerationComplete(
                        task.getTaskId(), 
                        task.getFilePath(), 
                        task.getGenerationId(),
                        fullContent.toString(),
                        fullContent.length()
                    );
                    
                    // 清理任务
                    activeTasks.remove(task.getGenerationId());
                })
                .doOnError(error -> {
                    logger.error("❌ 代码生成错误: {}", error.getMessage());
                    
                    // 通知生成错误
                    logStreamService.pushCodeGenerationError(
                        task.getTaskId(), 
                        task.getFilePath(), 
                        task.getGenerationId(),
                        error.getMessage()
                    );
                    
                    // 清理任务
                    activeTasks.remove(task.getGenerationId());
                })
                .subscribe();

        } catch (Exception e) {
            logger.error("❌ 代码生成任务执行失败: {}", e.getMessage(), e);
            
            // 通知生成错误
            logStreamService.pushCodeGenerationError(
                task.getTaskId(), 
                task.getFilePath(), 
                task.getGenerationId(),
                e.getMessage()
            );
            
            // 清理任务
            activeTasks.remove(task.getGenerationId());
        }
    }

    /**
     * 构建代码生成提示
     */
    private String buildCodeGenerationPrompt(String originalPrompt, String fileType, String filePath) {
        StringBuilder promptBuilder = new StringBuilder();
        
        promptBuilder.append("You are a code generation assistant. ");
        promptBuilder.append("Generate ONLY the code content for the file, without any explanations, comments about the task, or markdown formatting. ");
        promptBuilder.append("Do not include ```java or ``` or any other markdown code blocks. ");
        promptBuilder.append("Just return the pure code content that should be written to the file.\n\n");
        
        if (fileType != null && !fileType.isEmpty()) {
            promptBuilder.append("File type: ").append(fileType).append("\n");
        }
        
        promptBuilder.append("File path: ").append(filePath).append("\n\n");
        promptBuilder.append("Requirements: ").append(originalPrompt).append("\n\n");
        promptBuilder.append("Generate the complete code content:");
        
        return promptBuilder.toString();
    }

    /**
     * 追加内容到文件
     */
    private void appendToFile(String filePath, String content) {
        try {
            Path path = Paths.get(filePath);
            Files.writeString(path, content, StandardCharsets.UTF_8, StandardOpenOption.APPEND);
        } catch (Exception e) {
            logger.error("❌ 写入文件失败: {}", e.getMessage());
        }
    }

    /**
     * 停止代码生成任务
     */
    public boolean stopCodeGeneration(String generationId) {
        CodeGenerationTask task = activeTasks.remove(generationId);
        if (task != null) {
            logger.info("🛑 停止代码生成任务: {}", generationId);
            return true;
        }
        return false;
    }

    /**
     * 获取活跃任务数量
     */
    public int getActiveTaskCount() {
        return activeTasks.size();
    }

    /**
     * 代码生成任务
     */
    private static class CodeGenerationTask {
        private final String generationId;
        private final String taskId;
        private final String filePath;
        private final String prompt;
        private final String fileType;
        private final long startTime;

        public CodeGenerationTask(String generationId, String taskId, String filePath, String prompt, String fileType) {
            this.generationId = generationId;
            this.taskId = taskId;
            this.filePath = filePath;
            this.prompt = prompt;
            this.fileType = fileType;
            this.startTime = System.currentTimeMillis();
        }

        // Getters
        public String getGenerationId() { return generationId; }
        public String getTaskId() { return taskId; }
        public String getFilePath() { return filePath; }
        public String getPrompt() { return prompt; }
        public String getFileType() { return fileType; }
        public long getStartTime() { return startTime; }
    }
}
